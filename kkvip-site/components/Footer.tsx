import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="bg-gray-900 border-t border-gray-800 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10 sm:py-14">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 sm:gap-10">
          <div className="col-span-1 sm:col-span-2 lg:col-span-2">
            <h3 className="text-2xl sm:text-3xl font-bold mb-4 sm:mb-6 text-yellow-400">KKVIP Brasil</h3>
            <p className="text-gray-300 mb-6 sm:mb-8 text-base sm:text-lg leading-relaxed">
              A plataforma online líder em benefícios e promoções no Brasil.
              Cadastre-se gr<PERSON>tis e descubra um mundo de oportunidades exclusivas.
            </p>
            <div className="bg-gray-800 p-4 sm:p-6 rounded-lg mb-6">
              <Link
                href="https://1wuafz.life/?open=register&p=7fjh"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block bg-yellow-400 hover:bg-yellow-500 text-gray-900 px-6 py-3 rounded-md text-base font-semibold transition-colors touch-manipulation min-h-[48px] flex items-center justify-center"
              >
                Cadastrar Agora
              </Link>
            </div>
            <p className="text-gray-400 text-sm sm:text-base">
              © 2025 KKVIP Brasil. Todos os direitos reservados.
            </p>
          </div>
          
          <div>
            <h4 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 text-white">Links Rápidos</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/como-funciona" className="text-gray-300 hover:text-yellow-400 transition-colors text-sm sm:text-base">
                  Como Funciona
                </Link>
              </li>
              <li>
                <Link href="/promocoes" className="text-gray-300 hover:text-yellow-400 transition-colors text-sm sm:text-base">
                  Promoções
                </Link>
              </li>
              <li>
                <Link href="/faq" className="text-gray-300 hover:text-yellow-400 transition-colors text-sm sm:text-base">
                  FAQ
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-gray-300 hover:text-yellow-400 transition-colors text-sm sm:text-base">
                  Blog
                </Link>
              </li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 text-white">Suporte</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/contato" className="text-gray-300 hover:text-yellow-400 transition-colors text-sm sm:text-base">
                  Contato
                </Link>
              </li>
              <li>
                <Link href="/politica-privacidade" className="text-gray-300 hover:text-yellow-400 transition-colors text-sm sm:text-base">
                  Política de Privacidade
                </Link>
              </li>
              <li>
                <Link href="/termos-uso" className="text-gray-300 hover:text-yellow-400 transition-colors text-sm sm:text-base">
                  Termos de Uso
                </Link>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-700 mt-6 sm:mt-8 pt-6 sm:pt-8 flex flex-col sm:flex-row justify-between items-center">
          <p className="text-gray-400 text-xs sm:text-sm text-center sm:text-left mb-4 sm:mb-0">
            KKVIP Brasil - Sua plataforma de benefícios online
          </p>
          <div className="flex flex-wrap justify-center sm:justify-end space-x-4 gap-y-2">
            <Link href="/politica-privacidade" className="text-gray-400 hover:text-yellow-400 text-xs sm:text-sm transition-colors">
              Privacidade
            </Link>
            <Link href="/termos-uso" className="text-gray-400 hover:text-yellow-400 text-xs sm:text-sm transition-colors">
              Termos
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}