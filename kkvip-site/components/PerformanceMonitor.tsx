'use client';

import { useEffect, useState } from 'react';

interface PerformanceMetrics {
  loadTime: number;
  domContentLoaded: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
}

export default function PerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    const measurePerformance = () => {
      if (typeof window !== 'undefined' && 'performance' in window) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        // Get paint metrics
        const paintEntries = performance.getEntriesByType('paint');
        const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
        
        // Get LCP
        let lcp = 0;
        if ('PerformanceObserver' in window) {
          try {
            const observer = new PerformanceObserver((list) => {
              const entries = list.getEntries();
              const lastEntry = entries[entries.length - 1];
              lcp = lastEntry.startTime;
            });
            observer.observe({ entryTypes: ['largest-contentful-paint'] });
          } catch (e) {
            // LCP not supported
          }
        }

        const newMetrics: PerformanceMetrics = {
          loadTime: navigation.loadEventEnd - navigation.navigationStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
          firstContentfulPaint: fcp ? fcp.startTime : 0,
          largestContentfulPaint: lcp
        };

        setMetrics(newMetrics);
      }
    };

    // Measure after page load
    if (document.readyState === 'complete') {
      setTimeout(measurePerformance, 1000);
    } else {
      window.addEventListener('load', () => {
        setTimeout(measurePerformance, 1000);
      });
    }

    // Toggle visibility with keyboard shortcut
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        setIsVisible(!isVisible);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isVisible]);

  if (process.env.NODE_ENV !== 'development' || !metrics) {
    return null;
  }

  return (
    <>
      {/* Toggle button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full shadow-lg z-50 transition-colors"
        title="Toggle Performance Metrics (Ctrl+P)"
      >
        📊
      </button>

      {/* Performance panel */}
      {isVisible && (
        <div className="fixed bottom-16 right-4 bg-black bg-opacity-90 text-white text-xs p-4 rounded-lg z-50 max-w-xs">
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-bold text-sm">Performance Metrics</h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-400 hover:text-white"
            >
              ✕
            </button>
          </div>
          
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Load Time:</span>
              <span className={metrics.loadTime < 2000 ? 'text-green-400' : metrics.loadTime < 4000 ? 'text-yellow-400' : 'text-red-400'}>
                {Math.round(metrics.loadTime)}ms
              </span>
            </div>
            
            <div className="flex justify-between">
              <span>DOM Ready:</span>
              <span className={metrics.domContentLoaded < 1500 ? 'text-green-400' : metrics.domContentLoaded < 3000 ? 'text-yellow-400' : 'text-red-400'}>
                {Math.round(metrics.domContentLoaded)}ms
              </span>
            </div>
            
            {metrics.firstContentfulPaint > 0 && (
              <div className="flex justify-between">
                <span>FCP:</span>
                <span className={metrics.firstContentfulPaint < 1800 ? 'text-green-400' : metrics.firstContentfulPaint < 3000 ? 'text-yellow-400' : 'text-red-400'}>
                  {Math.round(metrics.firstContentfulPaint)}ms
                </span>
              </div>
            )}
            
            {metrics.largestContentfulPaint > 0 && (
              <div className="flex justify-between">
                <span>LCP:</span>
                <span className={metrics.largestContentfulPaint < 2500 ? 'text-green-400' : metrics.largestContentfulPaint < 4000 ? 'text-yellow-400' : 'text-red-400'}>
                  {Math.round(metrics.largestContentfulPaint)}ms
                </span>
              </div>
            )}
          </div>
          
          <div className="mt-2 pt-2 border-t border-gray-600 text-gray-400">
            <div>Press Ctrl+P to toggle</div>
          </div>
        </div>
      )}
    </>
  );
}
