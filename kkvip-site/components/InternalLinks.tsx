import Link from 'next/link';

interface RelatedLink {
  title: string;
  url: string;
  description: string;
}

interface InternalLinksProps {
  currentPage?: string;
  category?: string;
}

export function RelatedPages({ currentPage, category }: InternalLinksProps) {
  // Define related pages based on current page context
  const getRelatedPages = (): RelatedLink[] => {
    const allPages = {
      home: [
        { title: "Como Funciona o KKVIP", url: "/como-funciona", description: "Aprenda como usar a plataforma passo a passo" },
        { title: "Promoções Exclusivas", url: "/promocoes", description: "Confira as ofertas disponíveis agora" },
        { title: "Perguntas Frequentes", url: "/faq", description: "Tire suas dúvidas sobre o KKVIP" },
        { title: "Blog KKVIP", url: "/blog", description: "Dicas e novidades da plataforma" }
      ],
      comoFunciona: [
        { title: "Página Inicial", url: "/", description: "Conheça todos os benefícios do KKVIP" },
        { title: "Promoções Ativas", url: "/promocoes", description: "Veja as ofertas que você pode aproveitar" },
        { title: "Cadastro Gratuito", url: "/cadastro", description: "Comece agora mesmo no KKVIP" },
        { title: "FAQ - Dúvidas", url: "/faq", description: "Respostas para perguntas comuns" }
      ],
      promocoes: [
        { title: "Como Funciona", url: "/como-funciona", description: "Entenda como aproveitar as promoções" },
        { title: "Cadastre-se Grátis", url: "/cadastro", description: "Registre-se para acessar ofertas exclusivas" },
        { title: "Blog - Dicas", url: "/blog", description: "Estratégias para maximizar benefícios" },
        { title: "FAQ", url: "/faq", description: "Dúvidas sobre promoções e benefícios" }
      ],
      faq: [
        { title: "Página Inicial", url: "/", description: "Descubra os benefícios do KKVIP" },
        { title: "Como Funciona", url: "/como-funciona", description: "Guia passo a passo da plataforma" },
        { title: "Promoções", url: "/promocoes", description: "Ofertas exclusivas para membros" },
        { title: "Blog", url: "/blog", description: "Conteúdo educativo sobre KKVIP" }
      ],
      blog: [
        { title: "Guia: Como Funciona", url: "/como-funciona", description: "Tutorial completo da plataforma" },
        { title: "Promoções Atuais", url: "/promocoes", description: "Ofertas que você pode aproveitar hoje" },
        { title: "Cadastro", url: "/cadastro", description: "Junte-se à comunidade KKVIP" },
        { title: "FAQ", url: "/faq", description: "Respostas para suas dúvidas" }
      ]
    };

    return allPages[currentPage as keyof typeof allPages] || allPages.home;
  };

  const relatedPages = getRelatedPages();

  return (
    <section className="py-12 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
          Continue Explorando
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {relatedPages.map((page, index) => (
            <Link
              key={index}
              href={page.url}
              className="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow group"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                {page.title}
              </h3>
              <p className="text-gray-600 text-sm mb-3">
                {page.description}
              </p>
              <span className="text-blue-600 text-sm font-medium group-hover:text-blue-800">
                Saiba mais →
              </span>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}

export function QuickNavigation() {
  const quickLinks = [
    { title: "Início", url: "/", icon: "🏠" },
    { title: "Como Funciona", url: "/como-funciona", icon: "📋" },
    { title: "Promoções", url: "/promocoes", icon: "🎁" },
    { title: "Blog", url: "/blog", icon: "📝" },
    { title: "FAQ", url: "/faq", icon: "❓" },
    { title: "Contato", url: "/contato", icon: "📞" }
  ];

  return (
    <div className="bg-blue-600 text-white py-4">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-center">
          <span className="text-sm font-medium mr-4 hidden sm:block">Navegação Rápida:</span>
          <div className="flex flex-wrap justify-center gap-3">
            {quickLinks.map((link, index) => (
              <Link
                key={index}
                href={link.url}
                className="flex items-center bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                <span className="mr-2">{link.icon}</span>
                {link.title}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export function PopularContent() {
  const popularPages = [
    { 
      title: "Guia Completo KKVIP 2024", 
      url: "/como-funciona", 
      views: "2.5K visualizações",
      description: "O guia mais completo sobre como usar o KKVIP"
    },
    { 
      title: "Melhores Promoções KKVIP", 
      url: "/promocoes", 
      views: "1.8K visualizações",
      description: "As ofertas mais procuradas pelos usuários"
    },
    { 
      title: "FAQ - Dúvidas Frequentes", 
      url: "/faq", 
      views: "1.2K visualizações",
      description: "Respostas para as perguntas mais comuns"
    },
    { 
      title: "Blog - Dicas Exclusivas", 
      url: "/blog", 
      views: "980 visualizações",
      description: "Conteúdo atualizado semanalmente"
    }
  ];

  return (
    <section className="py-16 bg-white border-t border-gray-200">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
          📈 Conteúdo Mais Popular
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {popularPages.map((page, index) => (
            <Link
              key={index}
              href={page.url}
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group"
            >
              <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4 group-hover:bg-blue-200">
                <span className="text-xl">📄</span>
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 group-hover:text-blue-700">
                  {page.title}
                </h3>
                <p className="text-sm text-gray-600 mb-1">
                  {page.description}
                </p>
                <span className="text-xs text-gray-500">
                  {page.views}
                </span>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}

export function SiteMap() {
  const siteStructure = [
    {
      category: "Páginas Principais",
      pages: [
        { title: "Página Inicial", url: "/" },
        { title: "Como Funciona", url: "/como-funciona" },
        { title: "Promoções", url: "/promocoes" },
        { title: "FAQ", url: "/faq" },
        { title: "Blog", url: "/blog" }
      ]
    },
    {
      category: "Conta e Suporte",
      pages: [
        { title: "Cadastro", url: "/cadastro" },
        { title: "Login", url: "/login" },
        { title: "Contato", url: "/contato" },
        { title: "Política de Privacidade", url: "/politica-privacidade" },
        { title: "Termos de Uso", url: "/termos-uso" }
      ]
    },
    {
      category: "Conteúdo",
      pages: [
        { title: "Guias", url: "/guias" },
        { title: "Novidades", url: "/blog/novidades" },
        { title: "Dicas e Estratégias", url: "/blog/dicas" },
        { title: "Comparativos", url: "/blog/analises" }
      ]
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">
          Mapa do Site
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {siteStructure.map((section, index) => (
            <div key={index} className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-xl font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2">
                {section.category}
              </h3>
              <ul className="space-y-3">
                {section.pages.map((page, pageIndex) => (
                  <li key={pageIndex}>
                    <Link
                      href={page.url}
                      className="text-blue-600 hover:text-blue-800 hover:underline transition-colors"
                    >
                      {page.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}