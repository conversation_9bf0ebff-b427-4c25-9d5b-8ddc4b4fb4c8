'use client';

import Link from 'next/link';
import { useState } from 'react';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <header className="bg-gray-900 border-b border-gray-700 sticky top-0 z-50">
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16 sm:h-18">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="text-xl sm:text-2xl font-bold text-yellow-400 hover:text-yellow-300 transition-colors">
              KKVIP
            </Link>
          </div>
          
          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              <Link 
                href="/" 
                className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Início
              </Link>
              <Link 
                href="/como-funciona" 
                className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Como Funciona
              </Link>
              <Link 
                href="/promocoes" 
                className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Promoções
              </Link>
              <Link 
                href="/blog" 
                className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Blog
              </Link>
              <Link 
                href="/contato" 
                className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Contato
              </Link>
              <Link 
                href="/faq" 
                className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                FAQ
              </Link>
            </div>
          </div>
          
          {/* Desktop CTA */}
          <div className="hidden md:block">
            <div className="ml-4 flex items-center md:ml-6 space-x-3">
              <Link
                href="https://1wuafz.life/?open=register&p=7fjh"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-yellow-400 hover:bg-yellow-500 text-gray-900 px-4 py-2 rounded-md text-sm font-semibold transition-colors"
              >
                Cadastre-se Grátis
              </Link>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={toggleMenu}
              className="text-gray-300 hover:text-white p-3 rounded-md transition-colors touch-manipulation"
              aria-label="Abrir menu"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className={`md:hidden transition-all duration-300 ease-in-out ${isMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}`}>
          <div className="px-3 pt-3 pb-4 space-y-2 bg-gray-800 rounded-lg mt-3 shadow-lg">
            <Link
              href="/"
              onClick={closeMenu}
              className="text-gray-300 hover:text-white block px-4 py-3 rounded-md text-base font-medium transition-colors touch-manipulation"
            >
              Início
            </Link>
            <Link
              href="/como-funciona"
              onClick={closeMenu}
              className="text-gray-300 hover:text-white block px-4 py-3 rounded-md text-base font-medium transition-colors touch-manipulation"
            >
              Como Funciona
            </Link>
            <Link
              href="/promocoes"
              onClick={closeMenu}
              className="text-gray-300 hover:text-white block px-4 py-3 rounded-md text-base font-medium transition-colors touch-manipulation"
            >
              Promoções
            </Link>
            <Link
              href="/blog"
              onClick={closeMenu}
              className="text-gray-300 hover:text-white block px-4 py-3 rounded-md text-base font-medium transition-colors touch-manipulation"
            >
              Blog
            </Link>
            <Link
              href="/contato"
              onClick={closeMenu}
              className="text-gray-300 hover:text-white block px-4 py-3 rounded-md text-base font-medium transition-colors touch-manipulation"
            >
              Contato
            </Link>
            <Link
              href="/faq"
              onClick={closeMenu}
              className="text-gray-300 hover:text-white block px-4 py-3 rounded-md text-base font-medium transition-colors touch-manipulation"
            >
              FAQ
            </Link>

            {/* Mobile CTA */}
            <div className="pt-4 border-t border-gray-700 mt-4">
              <Link
                href="https://1wuafz.life/?open=register&p=7fjh"
                target="_blank"
                rel="noopener noreferrer"
                onClick={closeMenu}
                className="bg-yellow-400 hover:bg-yellow-500 text-gray-900 block px-4 py-3 rounded-md text-base font-semibold transition-colors text-center touch-manipulation min-h-[48px] flex items-center justify-center"
              >
                Cadastre-se Grátis
              </Link>
            </div>
          </div>
        </div>
      </nav>
    </header>
  );
}