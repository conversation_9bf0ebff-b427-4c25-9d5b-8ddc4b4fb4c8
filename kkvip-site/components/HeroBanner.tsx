'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

export default function HeroBanner() {
  const [imageLoaded, setImageLoaded] = useState(false);

  return (
    <section className="relative w-full h-[300px] sm:h-[400px] md:h-[500px] lg:h-[600px] overflow-hidden">
      {/* Background Image with Next.js optimization */}
      <div className="absolute inset-0">
        {/* Desktop WebP */}
        <Image
          src="/hero-banner.webp"
          alt="KKVIP Brasil - Cassino e Jogos Online"
          fill
          priority
          className={`hidden sm:block object-cover transition-opacity duration-500 ${
            imageLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          onLoad={() => setImageLoaded(true)}
          sizes="(max-width: 640px) 800px, 1200px"
          quality={85}
        />

        {/* Mobile WebP */}
        <Image
          src="/hero-banner-mobile.webp"
          alt="KKVIP Brasil - Cassino e Jogos Online"
          fill
          priority
          className={`block sm:hidden object-cover transition-opacity duration-500 ${
            imageLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          onLoad={() => setImageLoaded(true)}
          sizes="800px"
          quality={75}
        />

        {/* Fallback JPG for desktop */}
        <Image
          src="/hero-banner.jpg"
          alt="KKVIP Brasil - Cassino e Jogos Online"
          fill
          className={`hidden sm:block object-cover transition-opacity duration-500 ${
            imageLoaded ? 'opacity-0' : 'opacity-100'
          }`}
          sizes="(max-width: 640px) 800px, 1200px"
          quality={85}
        />

        {/* Fallback JPG for mobile */}
        <Image
          src="/hero-banner-mobile.jpg"
          alt="KKVIP Brasil - Cassino e Jogos Online"
          fill
          className={`block sm:hidden object-cover transition-opacity duration-500 ${
            imageLoaded ? 'opacity-0' : 'opacity-100'
          }`}
          sizes="800px"
          quality={75}
        />
      </div>

      {/* Overlay for better text readability */}
      <div className="absolute inset-0 bg-black bg-opacity-40"></div>

      {/* Content Overlay */}
      <div className="relative z-10 h-full flex items-center justify-center">
        <div className="container mx-auto px-6 sm:px-8 lg:px-12 text-center">
          <div className="max-w-4xl mx-auto space-y-6 sm:space-y-8">
            {/* Main Heading */}
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-white leading-tight">
              <span className="block mb-2">Bem-vindo ao</span>
              <span className="text-yellow-400 block drop-shadow-lg">KKVIP Brasil</span>
            </h1>

            {/* Subtitle */}
            <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl text-white font-semibold drop-shadow-lg">
              🎰 Ganhe Grandes Prêmios 🎰
            </p>

            {/* Description */}
            <p className="text-base sm:text-lg md:text-xl text-gray-200 max-w-3xl mx-auto leading-relaxed px-4">
              A plataforma online líder em benefícios e promoções no Brasil. Cadastre-se grátis e descubra um mundo de oportunidades exclusivas.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col gap-4 sm:flex-row sm:gap-6 sm:justify-center max-w-lg sm:max-w-xl mx-auto px-6">
              <Link 
                href="https://1wuafz.life/?open=register&p=7fjh"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 px-8 py-4 rounded-lg text-base sm:text-lg font-bold transition-all duration-300 text-center shadow-2xl min-h-[56px] flex items-center justify-center transform hover:scale-105"
              >
                🎯 Cadastre-se Grátis
              </Link>
              <Link 
                href="/como-funciona" 
                className="border-2 border-white hover:bg-white hover:text-gray-900 text-white px-8 py-4 rounded-lg text-base sm:text-lg font-semibold transition-all duration-300 text-center min-h-[56px] flex items-center justify-center backdrop-blur-sm"
              >
                📖 Como Funciona
              </Link>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center items-center gap-6 sm:gap-8 text-white text-sm sm:text-base opacity-90">
              <div className="flex items-center gap-2">
                <span className="text-green-400">✓</span>
                <span>100% Seguro</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-green-400">✓</span>
                <span>Cadastro Grátis</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-green-400">✓</span>
                <span>Suporte 24/7</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Animated elements */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Floating particles */}
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-yellow-400 rounded-full opacity-60 animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-3 h-3 bg-yellow-300 rounded-full opacity-40 animate-bounce"></div>
        <div className="absolute top-1/2 left-1/6 w-1 h-1 bg-white rounded-full opacity-80 animate-ping"></div>
      </div>
    </section>
  );
}
