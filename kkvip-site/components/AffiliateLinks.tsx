'use client';

import { trackEvent } from './Analytics';

interface AffiliateLinkProps {
  href: string;
  children: React.ReactNode;
  campaign?: string;
  className?: string;
  target?: string;
  rel?: string;
}

export function AffiliateLink({ 
  href, 
  children, 
  campaign = 'default', 
  className = '',
  target = '_blank',
  rel = 'noopener noreferrer'
}: AffiliateLinkProps) {
  const handleClick = () => {
    trackEvent('affiliate_click', 'engagement', campaign);
  };

  // Add UTM parameters for tracking
  const urlWithTracking = new URL(href);
  urlWithTracking.searchParams.set('utm_source', 'kkvip');
  urlWithTracking.searchParams.set('utm_medium', 'affiliate');
  urlWithTracking.searchParams.set('utm_campaign', campaign);
  urlWithTracking.searchParams.set('utm_content', 'website');

  return (
    <a
      href={urlWithTracking.toString()}
      className={className}
      target={target}
      rel={rel}
      onClick={handleClick}
    >
      {children}
    </a>
  );
}

// Example affiliate offers component
export function AffiliateOffers() {
  const offers = [
    {
      id: 1,
      title: "KKVIP Oficial",
      description: "Cadastre-se grátis na plataforma oficial KKVIP e aproveite todos os benefícios exclusivos",
      url: "https://1wuafz.life/?open=register&p=7fjh",
      campaign: "kkvip_official",
      bonus: "Cadastro Grátis",
      featured: true
    },
    {
      id: 2,
      title: "Benefícios VIP", 
      description: "Acesso completo a promoções exclusivas e programa de fidelidade",
      url: "https://1wuafz.life/?open=register&p=7fjh",
      campaign: "vip_benefits",
      bonus: "Acesso VIP",
      featured: false
    },
    {
      id: 3,
      title: "Promoções Especiais",
      description: "Participe de eventos exclusivos e receba bônus especiais para membros",
      url: "https://1wuafz.life/?open=register&p=7fjh", 
      campaign: "special_promotions",
      bonus: "Bônus Exclusivos",
      featured: false
    }
  ];

  return (
    <section className="py-16 bg-gradient-to-br from-gray-800 to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ofertas Exclusivas dos Nossos Parceiros
          </h2>
          <p className="text-xl text-gray-300">
            Aproveite benefícios únicos disponíveis apenas para membros KKVIP
          </p>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 sm:gap-8">
          {offers.map((offer) => (
            <div 
              key={offer.id} 
              className={`bg-gray-700 rounded-lg shadow-lg overflow-hidden ${
                offer.featured ? 'ring-2 ring-yellow-400 transform scale-105' : ''
              }`}
            >
              {offer.featured && (
                <div className="bg-yellow-400 text-gray-900 px-4 py-2 text-center font-semibold">
                  🌟 OFERTA DESTAQUE
                </div>
              )}
              
              <div className="p-6">
                <h3 className="text-xl font-bold mb-3 text-white">{offer.title}</h3>
                <p className="text-gray-300 mb-4">{offer.description}</p>
                
                <div className="bg-blue-900 rounded-lg p-4 mb-6">
                  <div className="text-center">
                    <span className="text-2xl font-bold text-blue-400">{offer.bonus}</span>
                    <p className="text-sm text-gray-300 mt-1">Exclusivo para membros</p>
                  </div>
                </div>
                
                <AffiliateLink
                  href={offer.url}
                  campaign={offer.campaign}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-semibold transition-colors block text-center"
                >
                  Aproveitar Oferta
                </AffiliateLink>
                
                <p className="text-xs text-gray-400 mt-3 text-center">
                  ⚠️ Oferta válida apenas para novos usuários. Termos e condições aplicáveis.
                </p>
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <p className="text-sm text-gray-300 mb-4">
            KKVIP pode receber comissões das ofertas apresentadas. Isso nos ajuda a manter a plataforma gratuita.
          </p>
          <div className="bg-gray-700 rounded-lg p-6 max-w-2xl mx-auto">
            <h3 className="font-semibold text-white mb-2">💡 Dica Importante:</h3>
            <p className="text-gray-300 text-sm">
              Sempre leia os termos e condições antes de participar de qualquer oferta. 
              Nossos parceiros são cuidadosamente selecionados, mas cada oferta tem suas próprias regras.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}