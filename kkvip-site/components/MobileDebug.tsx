'use client';

import { useEffect, useState } from 'react';

export default function MobileDebug() {
  const [screenInfo, setScreenInfo] = useState({
    width: 0,
    height: 0,
    devicePixelRatio: 0,
    userAgent: ''
  });

  useEffect(() => {
    const updateScreenInfo = () => {
      setScreenInfo({
        width: window.innerWidth,
        height: window.innerHeight,
        devicePixelRatio: window.devicePixelRatio || 1,
        userAgent: navigator.userAgent
      });
    };

    updateScreenInfo();
    window.addEventListener('resize', updateScreenInfo);

    return () => {
      window.removeEventListener('resize', updateScreenInfo);
    };
  }, []);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 bg-black bg-opacity-80 text-white text-xs p-2 rounded z-50 max-w-xs">
      <div>W: {screenInfo.width}px</div>
      <div>H: {screenInfo.height}px</div>
      <div>DPR: {screenInfo.devicePixelRatio}</div>
      <div className="truncate">UA: {screenInfo.userAgent.slice(0, 30)}...</div>
    </div>
  );
}
