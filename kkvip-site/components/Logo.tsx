import Image from 'next/image';

interface LogoProps {
  size?: number;
  className?: string;
  showText?: boolean;
}

export default function Logo({ size = 40, className = '', showText = true }: LogoProps) {
  return (
    <div className={`flex items-center gap-3 ${className}`}>
      {/* Logo Icon */}
      <div 
        className="flex-shrink-0 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center shadow-lg"
        style={{ width: size, height: size }}
      >
        <div className="text-white font-black text-center leading-none">
          <div 
            className="block"
            style={{ fontSize: size * 0.25, lineHeight: size * 0.25 + 'px' }}
          >
            KK
          </div>
          <div 
            className="block"
            style={{ fontSize: size * 0.25, lineHeight: size * 0.25 + 'px' }}
          >
            VIP
          </div>
        </div>
      </div>
      
      {/* Logo Text */}
      {showText && (
        <span className="text-xl sm:text-2xl font-bold text-yellow-400">
          KKVIP
        </span>
      )}
    </div>
  );
}
