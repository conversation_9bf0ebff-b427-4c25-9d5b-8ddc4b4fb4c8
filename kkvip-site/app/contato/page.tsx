import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Contato - KKVIP Brasil',
  description: 'Entre em contato com a equipe KKVIP Brasil. Oferecemos atendimento especializado para esclarecer dúvidas e fornecer suporte completo.',
  keywords: ['contato kkvip', 'suporte kkvip', 'atendimento kkvip brasil', 'fale conosco'],
  openGraph: {
    title: 'Contato - KKVIP Brasil',
    description: 'Entre em contato com nossa equipe especializada para suporte e esclarecimento de dúvidas.',
    url: 'https://www.kkvip.com.br/contato',
  },
};

export default function ContatoPage() {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="max-w-4xl mx-auto px-4 py-16">
        <h1 className="text-4xl font-bold mb-8 text-center">Entre em Contato</h1>
        
        <div className="bg-gray-800 rounded-lg shadow-lg p-8 mb-8">
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h2 className="text-2xl font-semibold mb-6 text-blue-400">Informações de Contato</h2>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="bg-blue-600 rounded-full p-2">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold">Email</h3>
                    <p className="text-gray-300"><EMAIL></p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="bg-blue-600 rounded-full p-2">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold">Horário de Atendimento</h3>
                    <p className="text-gray-300">Segunda à Sexta: 8h às 18h</p>
                    <p className="text-gray-300">Sábado: 8h às 14h</p>
                  </div>
                </div>
              </div>
              
              <div className="mt-8 p-4 bg-blue-900 rounded-lg">
                <h3 className="font-semibold mb-2">Cadastre-se na KKVIP</h3>
                <p className="text-sm text-gray-300 mb-3">
                  Acesse nossa plataforma oficial e comece a aproveitar todos os benefícios exclusivos.
                </p>
                <Link 
                  href="https://1wuafz.life/?open=register&p=7fjh"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-semibold"
                >
                  Cadastrar Agora
                </Link>
              </div>
            </div>
            
            <div>
              <h2 className="text-2xl font-semibold mb-6 text-blue-400">Perguntas Frequentes</h2>
              
              <div className="space-y-4">
                <div className="bg-gray-700 rounded-lg p-4">
                  <h3 className="font-semibold mb-2">Como me cadastro na KKVIP?</h3>
                  <p className="text-sm text-gray-300">
                    O cadastro é gratuito e pode ser feito através do nosso link oficial. 
                    Basta clicar no botão "Cadastrar Agora" e seguir as instruções.
                  </p>
                </div>
                
                <div className="bg-gray-700 rounded-lg p-4">
                  <h3 className="font-semibold mb-2">Quais são os benefícios?</h3>
                  <p className="text-sm text-gray-300">
                    Oferecemos promoções exclusivas, bônus especiais, programas de fidelidade 
                    e acesso a eventos e conteúdos únicos para nossos membros.
                  </p>
                </div>
                
                <div className="bg-gray-700 rounded-lg p-4">
                  <h3 className="font-semibold mb-2">É realmente gratuito?</h3>
                  <p className="text-sm text-gray-300">
                    Sim, o cadastro e a participação na plataforma são completamente gratuitos. 
                    Não cobramos taxas de inscrição ou mensalidades.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="text-center">
          <p className="text-gray-400 mb-4">
            Tem alguma dúvida específica? Visite nossa página de <Link href="/faq" className="text-blue-400 hover:text-blue-300">FAQ</Link> 
            ou conheça melhor <Link href="/como-funciona" className="text-blue-400 hover:text-blue-300">como funciona</Link> nossa plataforma.
          </p>
        </div>
      </div>
    </div>
  );
}