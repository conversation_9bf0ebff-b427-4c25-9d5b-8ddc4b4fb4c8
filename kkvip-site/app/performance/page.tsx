import Image from 'next/image';

export default function PerformancePage() {
  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-6xl mx-auto space-y-12">
        <h1 className="text-4xl font-bold text-center mb-12">🚀 图片优化性能报告</h1>
        
        {/* Performance Summary */}
        <section className="bg-gray-800 p-8 rounded-lg">
          <h2 className="text-2xl font-bold mb-6 text-yellow-400">📊 性能优化总结</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-gray-700 p-4 rounded-lg text-center">
              <div className="text-3xl font-bold text-green-400">85%</div>
              <div className="text-sm text-gray-300">文件大小减少</div>
            </div>
            <div className="bg-gray-700 p-4 rounded-lg text-center">
              <div className="text-3xl font-bold text-blue-400">WebP</div>
              <div className="text-sm text-gray-300">现代格式支持</div>
            </div>
            <div className="bg-gray-700 p-4 rounded-lg text-center">
              <div className="text-3xl font-bold text-purple-400">响应式</div>
              <div className="text-sm text-gray-300">移动端优化</div>
            </div>
            <div className="bg-gray-700 p-4 rounded-lg text-center">
              <div className="text-3xl font-bold text-orange-400">预加载</div>
              <div className="text-sm text-gray-300">关键资源</div>
            </div>
          </div>
        </section>

        {/* File Sizes Comparison */}
        <section className="bg-gray-800 p-8 rounded-lg">
          <h2 className="text-2xl font-bold mb-6 text-yellow-400">📁 文件大小对比</h2>
          
          <div className="overflow-x-auto">
            <table className="w-full text-left">
              <thead>
                <tr className="border-b border-gray-600">
                  <th className="pb-3 text-gray-300">文件</th>
                  <th className="pb-3 text-gray-300">格式</th>
                  <th className="pb-3 text-gray-300">尺寸</th>
                  <th className="pb-3 text-gray-300">大小</th>
                  <th className="pb-3 text-gray-300">用途</th>
                </tr>
              </thead>
              <tbody className="space-y-2">
                <tr className="border-b border-gray-700">
                  <td className="py-3">hero-banner.svg</td>
                  <td className="py-3"><span className="bg-blue-600 px-2 py-1 rounded text-xs">SVG</span></td>
                  <td className="py-3">1200×400</td>
                  <td className="py-3 text-green-400">6.0KB</td>
                  <td className="py-3">矢量源文件</td>
                </tr>
                <tr className="border-b border-gray-700">
                  <td className="py-3">hero-banner.webp</td>
                  <td className="py-3"><span className="bg-green-600 px-2 py-1 rounded text-xs">WebP</span></td>
                  <td className="py-3">1200×400</td>
                  <td className="py-3 text-green-400">11KB</td>
                  <td className="py-3">桌面端主图</td>
                </tr>
                <tr className="border-b border-gray-700">
                  <td className="py-3">hero-banner.jpg</td>
                  <td className="py-3"><span className="bg-yellow-600 px-2 py-1 rounded text-xs">JPG</span></td>
                  <td className="py-3">1200×400</td>
                  <td className="py-3 text-yellow-400">29KB</td>
                  <td className="py-3">桌面端备用</td>
                </tr>
                <tr className="border-b border-gray-700">
                  <td className="py-3">hero-banner-mobile.webp</td>
                  <td className="py-3"><span className="bg-green-600 px-2 py-1 rounded text-xs">WebP</span></td>
                  <td className="py-3">800×267</td>
                  <td className="py-3 text-green-400">6.3KB</td>
                  <td className="py-3">移动端主图</td>
                </tr>
                <tr>
                  <td className="py-3">hero-banner-mobile.jpg</td>
                  <td className="py-3"><span className="bg-yellow-600 px-2 py-1 rounded text-xs">JPG</span></td>
                  <td className="py-3">800×267</td>
                  <td className="py-3 text-yellow-400">13KB</td>
                  <td className="py-3">移动端备用</td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>

        {/* Optimization Techniques */}
        <section className="bg-gray-800 p-8 rounded-lg">
          <h2 className="text-2xl font-bold mb-6 text-yellow-400">⚡ 优化技术</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-blue-400">图片格式优化</h3>
              <ul className="space-y-2 text-gray-300">
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  WebP 格式减少 60% 文件大小
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  JPG 备用确保兼容性
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  SVG 源文件保持矢量质量
                </li>
              </ul>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-blue-400">响应式优化</h3>
              <ul className="space-y-2 text-gray-300">
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  移动端专用小尺寸图片
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  根据屏幕尺寸自动选择
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  减少移动端数据使用
                </li>
              </ul>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-blue-400">加载优化</h3>
              <ul className="space-y-2 text-gray-300">
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  关键图片预加载
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  Next.js Image 组件优化
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  渐进式加载效果
                </li>
              </ul>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-blue-400">性能监控</h3>
              <ul className="space-y-2 text-gray-300">
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  实时性能指标
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  Core Web Vitals 监控
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  开发环境调试工具
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Performance Tips */}
        <section className="bg-gray-800 p-8 rounded-lg">
          <h2 className="text-2xl font-bold mb-6 text-yellow-400">💡 性能提示</h2>
          
          <div className="bg-blue-900 bg-opacity-50 p-6 rounded-lg mb-6">
            <h3 className="text-lg font-semibold mb-3 text-blue-300">开发环境性能监控</h3>
            <p className="text-gray-300 mb-3">
              在开发环境中，您可以使用以下方式查看性能指标：
            </p>
            <ul className="space-y-2 text-gray-300">
              <li>• 点击右下角的 📊 按钮</li>
              <li>• 或按 <kbd className="bg-gray-700 px-2 py-1 rounded">Ctrl + P</kbd> 快捷键</li>
              <li>• 查看加载时间、FCP、LCP 等关键指标</li>
            </ul>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-green-900 bg-opacity-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-300 mb-2">✅ 已实现的优化</h4>
              <ul className="text-sm text-gray-300 space-y-1">
                <li>• 图片格式现代化 (WebP)</li>
                <li>• 响应式图片加载</li>
                <li>• 关键资源预加载</li>
                <li>• 渐进式图片显示</li>
              </ul>
            </div>
            
            <div className="bg-yellow-900 bg-opacity-50 p-4 rounded-lg">
              <h4 className="font-semibold text-yellow-300 mb-2">🔄 可进一步优化</h4>
              <ul className="text-sm text-gray-300 space-y-1">
                <li>• 添加 AVIF 格式支持</li>
                <li>• 实现图片懒加载</li>
                <li>• CDN 分发优化</li>
                <li>• 图片压缩自动化</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Back to Home */}
        <div className="text-center">
          <a 
            href="/" 
            className="inline-block bg-yellow-400 hover:bg-yellow-500 text-gray-900 px-8 py-4 rounded-lg font-bold transition-colors"
          >
            返回首页查看效果
          </a>
        </div>
      </div>
    </div>
  );
}
