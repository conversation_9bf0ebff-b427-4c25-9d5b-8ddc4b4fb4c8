import Link from 'next/link';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Promoções Exclusivas KKVIP - Ofertas Especiais Brasil',
  description: 'Descubra as promoções exclusivas KKVIP disponíveis apenas para membros. Bônus especiais, ofertas limitadas e benefícios únicos que você não encontra em outro lugar.',
  keywords: ['kkvip promoções', 'ofertas kkvip', 'bônus kkvip', 'promoções exclusivas', 'ofertas especiais brasil'],
  openGraph: {
    title: 'Promoções Exclusivas KKVIP - Ofertas Especiais',
    description: 'Aprovete promoções exclusivas disponíveis apenas para membros KKVIP.',
  },
};

export default function Promocoes() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-purple-600 to-blue-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Promoções Exclusivas KKVIP
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              Ofertas especiais e benefícios únicos disponíveis apenas para membros. 
              Cadastre-se grátis e aproveite agora mesmo!
            </p>
            <div className="bg-yellow-400 text-gray-900 px-6 py-3 rounded-full inline-block font-semibold">
              🎉 Novas promoções adicionadas semanalmente!
            </div>
          </div>
        </div>
      </section>

      {/* Current Promotions */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Promoções Ativas Agora
            </h2>
            <p className="text-xl text-gray-600">
              Aproveite essas ofertas limitadas antes que expirem
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Promotion 1 */}
            <div className="bg-white rounded-lg shadow-lg overflow-hidden border-2 border-yellow-400">
              <div className="bg-yellow-400 text-gray-900 px-4 py-2 text-center font-semibold">
                🔥 OFERTA QUENTE
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-bold mb-3 text-center">Bônus de Boas-vindas</h3>
                <div className="text-center mb-4">
                  <span className="text-4xl font-bold text-blue-600">100%</span>
                  <span className="text-lg text-gray-600 ml-2">de bônus</span>
                </div>
                <ul className="space-y-2 mb-6">
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Válido para novos membros
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Ativação instantânea
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Sem requisitos mínimos
                  </li>
                </ul>
                <Link 
                  href="/cadastro" 
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-semibold transition-colors block text-center"
                >
                  Reivindicar Agora
                </Link>
              </div>
            </div>

            {/* Promotion 2 */}
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="bg-blue-500 text-white px-4 py-2 text-center font-semibold">
                💎 EXCLUSIVO VIP
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-bold mb-3 text-center">Cashback Semanal</h3>
                <div className="text-center mb-4">
                  <span className="text-4xl font-bold text-purple-600">25%</span>
                  <span className="text-lg text-gray-600 ml-2">toda semana</span>
                </div>
                <ul className="space-y-2 mb-6">
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Calculado automaticamente
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Pagamento todas as segundas
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Sem limite máximo
                  </li>
                </ul>
                <Link 
                  href="/cadastro" 
                  className="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 px-4 rounded-lg font-semibold transition-colors block text-center"
                >
                  Participar Agora
                </Link>
              </div>
            </div>

            {/* Promotion 3 */}
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="bg-green-500 text-white px-4 py-2 text-center font-semibold">
                🎁 PROGRAMA FIDELIDADE
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-bold mb-3 text-center">Recompensas VIP</h3>
                <div className="text-center mb-4">
                  <span className="text-4xl font-bold text-green-600">50%</span>
                  <span className="text-lg text-gray-600 ml-2">extra</span>
                </div>
                <ul className="space-y-2 mb-6">
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Bônus progressivos
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Níveis de status
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Benefícios vitalícios
                  </li>
                </ul>
                <Link 
                  href="/cadastro" 
                  className="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-semibold transition-colors block text-center"
                >
                  Começar Agora
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Limited Time Offers */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Ofertas por Tempo Limitado
            </h2>
            <p className="text-xl text-gray-600">
              Aproveite antes que expire!
            </p>
          </div>
          
          <div className="bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-2xl p-8 mb-12">
            <div className="flex flex-col lg:flex-row items-center justify-between">
              <div className="mb-6 lg:mb-0">
                <h3 className="text-3xl font-bold mb-2">Super Bônus de Final de Semana</h3>
                <p className="text-xl mb-4">Triplicamos seu bônus nos fins de semana!</p>
                <div className="flex items-center space-x-4">
                  <span className="bg-white text-red-600 px-4 py-2 rounded-full font-bold">300% BÔNUS</span>
                  <span className="text-lg">Válido até domingo</span>
                </div>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-2">⏰</div>
                <Link 
                  href="/cadastro" 
                  className="bg-white text-red-600 hover:bg-gray-100 px-8 py-4 rounded-lg font-bold transition-colors inline-block"
                >
                  Aproveitar Oferta
                </Link>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-yellow-50 border-2 border-yellow-400 rounded-lg p-6">
              <div className="flex items-center mb-4">
                <span className="text-3xl mr-3">⚡</span>
                <h3 className="text-xl font-bold">Bônus Relâmpago</h3>
              </div>
              <p className="text-gray-600 mb-4">
                A cada 100 novos membros, liberamos um bônus especial de 200% para os próximos 24 cadastros.
              </p>
              <div className="bg-yellow-400 text-gray-900 px-4 py-2 rounded-full text-center font-semibold mb-4">
                Restam apenas 7 vagas!
              </div>
              <Link 
                href="/cadastro" 
                className="w-full bg-yellow-500 hover:bg-yellow-600 text-gray-900 py-3 px-4 rounded-lg font-semibold transition-colors block text-center"
              >
                Garantir Minha Vaga
              </Link>
            </div>
            
            <div className="bg-blue-50 border-2 border-blue-400 rounded-lg p-6">
              <div className="flex items-center mb-4">
                <span className="text-3xl mr-3">🎯</span>
                <h3 className="text-xl font-bold">Missão Diária</h3>
              </div>
              <p className="text-gray-600 mb-4">
                Complete tarefas simples todos os dias e ganhe bônus extras. Quanto mais dias consecutivos, maior o prêmio!
              </p>
              <div className="bg-blue-400 text-white px-4 py-2 rounded-full text-center font-semibold mb-4">
                Até 500% de bônus extra
              </div>
              <Link 
                href="/cadastro" 
                className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold transition-colors block text-center"
              >
                Começar Missões
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Terms and Conditions */}
      <section className="py-16 bg-gray-100">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Termos e Condições das Promoções
            </h2>
          </div>
          
          <div className="bg-white rounded-lg p-6 shadow-md">
            <h3 className="text-xl font-semibold mb-4">Informações Importantes:</h3>
            <ul className="space-y-3 text-gray-600">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2"></span>
                Todas as promoções são válidas apenas para novos membros KKVIP
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2"></span>
                É necessário ser maior de 18 anos para participar
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2"></span>
                Os bônus são creditados automaticamente após confirmação do cadastro
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2"></span>
                KKVIP se reserva o direito de modificar ou cancelar promoções sem aviso prévio
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2"></span>
                Para dúvidas, entre em contato com nosso suporte 24/7
              </li>
            </ul>
            
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <p className="text-blue-800 font-semibold">
                💡 Dica: Cadastre-se hoje mesmo para não perder nenhuma promoção exclusiva!
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Final */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Não Perca Essas Ofertas Incríveis!
          </h2>
          <p className="text-xl mb-8">
            Cadastre-se grátis agora e aproveite todas as promoções exclusivas KKVIP
          </p>
          <Link 
            href="/cadastro" 
            className="bg-yellow-400 hover:bg-yellow-500 text-gray-900 px-12 py-4 rounded-lg text-xl font-bold transition-colors inline-block"
          >
            Cadastrar e Aproveitar Todas as Ofertas
          </Link>
          <p className="text-sm mt-4 opacity-90">
            ✅ Grátis para sempre • ✅ Sem taxas ocultas • ✅ Ativação instantânea
          </p>
        </div>
      </section>
    </div>
  );
}