import Logo from '@/components/Logo';
import Image from 'next/image';

export default function TestLogoPage() {
  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto space-y-12">
        <h1 className="text-4xl font-bold text-center mb-12">KKVIP Logo 测试页面</h1>
        
        {/* Logo Component Tests */}
        <section className="space-y-8">
          <h2 className="text-2xl font-bold">Logo 组件测试</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">小尺寸 (32px)</h3>
              <Logo size={32} />
            </div>
            
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">中等尺寸 (48px)</h3>
              <Logo size={48} />
            </div>
            
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">大尺寸 (64px)</h3>
              <Logo size={64} />
            </div>
            
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">仅图标</h3>
              <Logo size={48} showText={false} />
            </div>
            
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">超大尺寸 (96px)</h3>
              <Logo size={96} />
            </div>
          </div>
        </section>

        {/* SVG Files */}
        <section className="space-y-8">
          <h2 className="text-2xl font-bold">SVG 文件</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">Logo SVG (200x200)</h3>
              <Image src="/logo.svg" alt="KKVIP Logo" width={200} height={200} />
            </div>
            
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">Favicon SVG (32x32)</h3>
              <Image src="/favicon.svg" alt="KKVIP Favicon" width={64} height={64} />
            </div>
          </div>
        </section>

        {/* PNG Files */}
        <section className="space-y-8">
          <h2 className="text-2xl font-bold">PNG 文件</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">Icon 192x192</h3>
              <Image src="/icon-192.png" alt="KKVIP Icon 192" width={96} height={96} />
            </div>
            
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">Icon 512x512</h3>
              <Image src="/icon-512.png" alt="KKVIP Icon 512" width={96} height={96} />
            </div>
            
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">Apple Touch Icon</h3>
              <Image src="/apple-touch-icon.png" alt="KKVIP Apple Touch Icon" width={96} height={96} />
            </div>
          </div>
        </section>

        {/* Favicon Test */}
        <section className="space-y-8">
          <h2 className="text-2xl font-bold">Favicon 测试</h2>
          
          <div className="bg-gray-800 p-6 rounded-lg">
            <p className="text-gray-300 mb-4">
              查看浏览器标签页的图标，应该显示 KKVIP logo。
            </p>
            <p className="text-gray-300">
              您也可以直接访问: <a href="/favicon.ico" className="text-yellow-400 hover:text-yellow-300" target="_blank">/favicon.ico</a>
            </p>
          </div>
        </section>

        {/* Back to Home */}
        <div className="text-center">
          <a 
            href="/" 
            className="inline-block bg-yellow-400 hover:bg-yellow-500 text-gray-900 px-8 py-4 rounded-lg font-bold transition-colors"
          >
            返回首页
          </a>
        </div>
      </div>
    </div>
  );
}
