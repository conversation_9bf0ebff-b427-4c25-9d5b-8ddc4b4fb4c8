import Link from 'next/link';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Blog KKVIP - Dicas, Guias e Novidades',
  description: 'Fique por dentro das últimas novidades, dicas exclusivas e guias completos sobre como maximizar seus benefícios no KKVIP. Conteúdo atualizado semanalmente.',
  keywords: ['kkvip blog', 'dicas kkvip', 'guias kkvip', 'novidades kkvip', 'como usar kkvip'],
  openGraph: {
    title: 'Blog KKVIP - Dicas, Guias e Novidades',
    description: 'Conteúdo exclusivo para maximizar seus benefícios no KKVIP.',
  },
};

const blogPosts = [
  {
    id: 1,
    title: "Guia Completo: Como Maximizar seus Benefícios no KKVIP em 2024",
    excerpt: "Descubra as estratégias mais eficazes para aproveitar ao máximo todos os benefícios e promoções disponíveis na plataforma KKVIP.",
    author: "Equipe KKVIP",
    date: "15 de Janeiro, 2024",
    readTime: "8 min",
    category: "Guias",
    slug: "guia-completo-maximizar-beneficios-kkvip-2024",
    featured: true,
    image: "/blog/guia-beneficios.jpg"
  },
  {
    id: 2,
    title: "5 Erros Comuns que Novos Membros Cometem no KKVIP",
    excerpt: "Evite esses erros básicos e comece sua jornada KKVIP da forma correta desde o primeiro dia.",
    author: "Maria Silva",
    date: "12 de Janeiro, 2024", 
    readTime: "5 min",
    category: "Dicas",
    slug: "5-erros-comuns-novos-membros-kkvip",
    featured: false,
    image: "/blog/erros-comuns.jpg"
  },
  {
    id: 3,
    title: "KKVIP vs Concorrentes: Comparativo Completo 2024",
    excerpt: "Análise detalhada das vantagens do KKVIP em relação a outras plataformas similares no mercado brasileiro.",
    author: "João Santos",
    date: "10 de Janeiro, 2024",
    readTime: "12 min", 
    category: "Análises",
    slug: "kkvip-vs-concorrentes-comparativo-2024",
    featured: false,
    image: "/blog/comparativo.jpg"
  },
  {
    id: 4,
    title: "Programa de Fidelidade KKVIP: Como Subir de Nível Rapidamente",
    excerpt: "Estratégias comprovadas para acelerar sua progressão no programa de fidelidade e desbloquear benefícios VIP.",
    author: "Ana Costa",
    date: "8 de Janeiro, 2024",
    readTime: "6 min",
    category: "Estratégias",
    slug: "programa-fidelidade-kkvip-subir-nivel",
    featured: false,
    image: "/blog/fidelidade.jpg"
  },
  {
    id: 5,
    title: "Novidades KKVIP: Tudo que Mudou em Janeiro 2024",
    excerpt: "Confira as principais atualizações, novas funcionalidades e melhorias implementadas na plataforma neste mês.",
    author: "Equipe KKVIP",
    date: "5 de Janeiro, 2024",
    readTime: "4 min",
    category: "Novidades", 
    slug: "novidades-kkvip-janeiro-2024",
    featured: false,
    image: "/blog/novidades.jpg"
  },
  {
    id: 6,
    title: "Como Aproveitar Promoções Sazonais no KKVIP",
    excerpt: "Dicas para ficar sempre por dentro das melhores ofertas e como aproveitá-las estrategicamente.",
    author: "Carlos Oliveira",
    date: "3 de Janeiro, 2024",
    readTime: "7 min",
    category: "Promoções",
    slug: "aproveitar-promocoes-sazonais-kkvip", 
    featured: false,
    image: "/blog/promocoes-sazonais.jpg"
  }
];

const categories = ["Todos", "Guias", "Dicas", "Análises", "Estratégias", "Novidades", "Promoções"];

export default function Blog() {
  const featuredPost = blogPosts.find(post => post.featured);
  const otherPosts = blogPosts.filter(post => !post.featured);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-blue-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Blog KKVIP
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              Dicas exclusivas, guias completos e as últimas novidades para maximizar 
              seus benefícios na plataforma líder do Brasil
            </p>
          </div>
        </div>
      </section>

      {/* Featured Post */}
      {featuredPost && (
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Artigo em Destaque
              </h2>
            </div>
            
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl overflow-hidden shadow-xl">
              <div className="lg:flex lg:items-center">
                <div className="lg:w-1/2 p-8 lg:p-12">
                  <div className="flex items-center mb-4">
                    <span className="bg-yellow-400 text-gray-900 px-3 py-1 rounded-full text-sm font-semibold mr-3">
                      ✨ DESTAQUE
                    </span>
                    <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                      {featuredPost.category}
                    </span>
                  </div>
                  
                  <h3 className="text-3xl font-bold text-gray-900 mb-4">
                    {featuredPost.title}
                  </h3>
                  
                  <p className="text-gray-600 text-lg mb-6">
                    {featuredPost.excerpt}
                  </p>
                  
                  <div className="flex items-center text-sm text-gray-500 mb-6">
                    <span>{featuredPost.author}</span>
                    <span className="mx-2">•</span>
                    <span>{featuredPost.date}</span>
                    <span className="mx-2">•</span>
                    <span>{featuredPost.readTime} de leitura</span>
                  </div>
                  
                  <Link
                    href={`/blog/${featuredPost.slug}`}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors inline-block"
                  >
                    Ler Artigo Completo
                  </Link>
                </div>
                
                <div className="lg:w-1/2 h-64 lg:h-96 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                  <div className="text-white text-center">
                    <div className="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                      </svg>
                    </div>
                    <p className="font-semibold">Artigo em Destaque</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Category Filter */}
      <section className="py-8 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-3">
            {categories.map((category) => (
              <button
                key={category}
                className="px-4 py-2 rounded-full text-sm font-medium transition-colors bg-white text-gray-700 hover:bg-blue-600 hover:text-white"
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {otherPosts.map((post) => (
              <article key={post.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <div className="h-48 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                  <div className="text-white text-center">
                    <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <p className="text-sm font-medium">{post.category}</p>
                  </div>
                </div>
                
                <div className="p-6">
                  <div className="flex items-center mb-3">
                    <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs font-medium">
                      {post.category}
                    </span>
                  </div>
                  
                  <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                    {post.title}
                  </h3>
                  
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>
                  
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <span>{post.author}</span>
                    <span>{post.readTime}</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">{post.date}</span>
                    <Link
                      href={`/blog/${post.slug}`}
                      className="text-blue-600 hover:text-blue-800 font-semibold text-sm"
                    >
                      Ler mais →
                    </Link>
                  </div>
                </div>
              </article>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Não Perca Nenhuma Novidade!
          </h2>
          <p className="text-xl mb-8">
            Cadastre-se para receber nossos artigos exclusivos e dicas de como maximizar seus benefícios KKVIP
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-lg mx-auto">
            <input
              type="email"
              placeholder="Seu melhor e-mail"
              className="flex-1 px-4 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-yellow-400"
            />
            <button className="bg-yellow-400 hover:bg-yellow-500 text-gray-900 px-6 py-3 rounded-lg font-semibold transition-colors">
              Cadastrar
            </button>
          </div>
          
          <p className="text-blue-100 text-sm mt-4">
            📧 Conteúdo semanal • 🎯 Dicas exclusivas • 🚫 Sem spam, prometemos!
          </p>
        </div>
      </section>

      {/* SEO Content */}
      <section className="py-16 bg-gray-100">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Por que Seguir o Blog KKVIP?
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-3">Conteúdo Exclusivo</h3>
              <p className="text-gray-600">
                Dicas e estratégias que você não encontra em outros lugares, 
                criadas especialmente para membros KKVIP.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-3">Sempre Atualizado</h3>
              <p className="text-gray-600">
                Novos artigos toda semana com as informações mais recentes 
                sobre funcionalidades e oportunidades.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-3">Especialistas</h3>
              <p className="text-gray-600">
                Conteúdo criado por especialistas que conhecem profundamente 
                a plataforma e suas melhores práticas.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}