import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { JsonLd } from "@/components/JsonLd";
import { Analytics } from "@/components/Analytics";
import MobileOptimizations from "@/components/MobileOptimizations";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  metadataBase: new URL('https://www.kkvip.com.br'),
  title: {
    default: 'KKVIP Brasil - Plataforma Online Líder em Benefícios',
    template: '%s | KKVIP Brasil'
  },
  description: 'Descubra KKVIP, a plataforma online líder no Brasil. Cadastre-se grátis e aproveite promoções exclusivas, bônus especiais e benefícios únicos.',
  keywords: ['kkvip', 'kkvip brasil', 'kkvip online', 'kkvip cadastro', 'kkvip grátis', 'plataforma online brasil'],
  authors: [{ name: 'KKVIP Brasil' }],
  creator: 'KKVIP Brasil',
  publisher: 'KKVIP Brasil',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    title: 'KKVIP Brasil - Plataforma Online Líder em Benefícios',
    description: 'Descubra KKVIP, a plataforma online líder no Brasil. Cadastre-se grátis e aproveite promoções exclusivas.',
    url: 'https://www.kkvip.com.br',
    siteName: 'KKVIP Brasil',
    locale: 'pt_BR',
    type: 'website',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'KKVIP Brasil - Plataforma Online'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'KKVIP Brasil - Plataforma Online Líder',
    description: 'Cadastre-se grátis no KKVIP e aproveite benefícios exclusivos.',
    images: ['/twitter-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'google-site-verification-code',
  },
  alternates: {
    canonical: 'https://www.kkvip.com.br',
    languages: {
      'pt-BR': 'https://www.kkvip.com.br',
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt-BR">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <JsonLd />
      </head>
      <body className={`${inter.className} antialiased`}>
        <MobileOptimizations />
        <Analytics />
        <Header />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
