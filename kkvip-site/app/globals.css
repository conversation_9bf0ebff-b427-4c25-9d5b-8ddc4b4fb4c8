@import "tailwindcss";

:root {
  --background: #0a0a0a;
  --foreground: #ededed;
  --accent: #3b82f6;
  --accent-foreground: #ffffff;
  --muted: #374151;
  --muted-foreground: #9ca3af;
  --border: #374151;
  --card: #111827;
  --card-foreground: #f3f4f6;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  /* Improve touch targets */
  button, a, input, select, textarea {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better text readability on mobile */
  body {
    font-size: 16px;
    line-height: 1.6;
  }

  /* Prevent zoom on input focus */
  input, select, textarea {
    font-size: 16px;
  }
}

/* Touch-friendly interactions */
.touch-manipulation {
  touch-action: manipulation;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}
