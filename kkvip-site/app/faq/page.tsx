import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'FAQ - Perguntas Frequentes sobre KKVIP Brasil',
  description: 'Encontre respostas para as principais dúvidas sobre KKVIP. Como funciona, como se cadastrar, benefício<PERSON>, segurança e muito mais. Suporte completo para usuários.',
  keywords: ['kkvip faq', 'perguntas frequentes kkvip', 'dúvidas kkvip', 'ajuda kkvip', 'suporte kkvip'],
  openGraph: {
    title: 'FAQ - Perguntas Frequentes sobre KKVIP',
    description: 'Tire todas suas dúvidas sobre o KKVIP Brasil.',
  },
};

const faqs = [
  {
    category: "Sobre o KKVIP",
    questions: [
      {
        q: "O que é o KKVIP?",
        a: "KKVIP é a plataforma online líder em benefícios e promoções no Brasil. Oferecemos acesso exclusivo a bônus especiais, promoções e vantagens únicas para nossos membros cadastrados."
      },
      {
        q: "KKVIP é confiável?",
        a: "Sim, KKVIP é uma plataforma 100% confiável com milhares de usuários satisfeitos em todo o Brasil. Utilizamos as mais avançadas tecnologias de segurança para proteger seus dados e garantir uma experiência segura."
      },
      {
        q: "O KKVIP é realmente grátis?",
        a: "Sim! O cadastro e uso básico do KKVIP são completamente gratuitos. Você pode acessar todos os benefícios principais sem pagar absolutamente nada. Não há taxas de inscrição, manutenção ou custos ocultos."
      }
    ]
  },
  {
    category: "Cadastro e Conta",
    questions: [
      {
        q: "Como faço para me cadastrar no KKVIP?",
        a: "O cadastro é muito simples! Clique em 'Cadastre-se Grátis', preencha suas informações básicas (nome, e-mail, confirmação de idade) e pronto. Sua conta será ativada instantaneamente."
      },
      {
        q: "Quais informações preciso fornecer para me cadastrar?",
        a: "Precisamos apenas de informações básicas: seu nome completo, e-mail válido e confirmação de que você tem mais de 18 anos. Não pedimos documentos ou informações financeiras no cadastro."
      },
      {
        q: "Posso ter mais de uma conta?",
        a: "Não, cada pessoa pode ter apenas uma conta KKVIP. Isso garante a segurança da plataforma e a distribuição justa de benefícios entre todos os membros."
      },
      {
        q: "Como faço para recuperar minha senha?",
        a: "Na tela de login, clique em 'Esqueci minha senha'. Digite seu e-mail cadastrado e enviaremos instruções para redefinir sua senha em poucos minutos."
      }
    ]
  },
  {
    category: "Benefícios e Promoções",
    questions: [
      {
        q: "Que tipo de benefícios posso esperar?",
        a: "Oferecemos diversos benefícios incluindo: bônus de boas-vindas, promoções exclusivas, cashback semanal, programa de fidelidade, acesso antecipado a ofertas especiais e muito mais."
      },
      {
        q: "Como recebo os benefícios?",
        a: "Os benefícios são creditados automaticamente em sua conta após o cadastro e ficam disponíveis em sua área de membro. Você também receberá notificações por e-mail sobre novas promoções."
      },
      {
        q: "Os benefícios expiram?",
        a: "Alguns benefícios podem ter prazos específicos, mas isso sempre será claramente informado. A maioria dos nossos benefícios principais não expira enquanto você mantiver sua conta ativa."
      },
      {
        q: "Como funciona o programa de fidelidade?",
        a: "Quanto mais você usa o KKVIP, mais benefícios desbloqueeia. Temos diferentes níveis de status que oferecem vantagens progressivas, incluindo bônus maiores, acesso prioritário e benefícios exclusivos VIP."
      }
    ]
  },
  {
    category: "Segurança",
    questions: [
      {
        q: "Meus dados pessoais estão seguros?",
        a: "Absolutamente! Utilizamos criptografia de ponta e seguimos rigorosos protocolos de segurança para proteger todas as informações pessoais. Seus dados nunca são compartilhados com terceiros sem sua autorização."
      },
      {
        q: "Como posso verificar se minha conta está segura?",
        a: "Recomendamos usar uma senha forte única para sua conta KKVIP. Você pode verificar a atividade de sua conta a qualquer momento em sua área de membro. Se notar algo suspeito, entre em contato conosco imediatamente."
      },
      {
        q: "O que faço se suspeitar de atividade suspeita?",
        a: "Entre em contato com nosso suporte 24/7 imediatamente através do chat ao vivo ou e-mail. Nossa equipe de segurança investigará qualquer atividade suspeita rapidamente."
      }
    ]
  },
  {
    category: "Suporte",
    questions: [
      {
        q: "Como posso entrar em contato com o suporte?",
        a: "Oferecemos suporte 24/7 através de chat ao vivo, e-mail e telefone. Membros KKVIP têm atendimento prioritário e tempo de resposta mais rápido."
      },
      {
        q: "Quanto tempo leva para receber uma resposta?",
        a: "Nosso objetivo é responder a todas as consultas em menos de 2 horas. Membros VIP recebem atendimento prioritário com resposta em até 30 minutos."
      },
      {
        q: "O suporte está disponível em português?",
        a: "Sim! Todo nosso suporte é oferecido em português brasileiro por uma equipe especializada que entende as necessidades específicas dos usuários brasileiros."
      }
    ]
  },
  {
    category: "Conta e Configurações",
    questions: [
      {
        q: "Posso alterar minhas informações pessoais?",
        a: "Sim, você pode atualizar suas informações pessoais a qualquer momento através da seção 'Minha Conta' em sua área de membro. Algumas mudanças podem requerer verificação adicional por segurança."
      },
      {
        q: "Como posso cancelar minha conta?",
        a: "Você pode cancelar sua conta a qualquer momento através das configurações da conta ou entrando em contato com nosso suporte. O cancelamento é processado imediatamente e você receberá confirmação por e-mail."
      },
      {
        q: "O que acontece com meus benefícios se cancelar a conta?",
        a: "Ao cancelar sua conta, você perderá acesso a todos os benefícios ativos. Recomendamos utilizar todos os benefícios disponíveis antes do cancelamento, pois eles não podem ser transferidos ou reembolsados."
      },
      {
        q: "Posso reativar minha conta após cancelamento?",
        a: "Sim, você pode reativar sua conta entrando em contato com nosso suporte. No entanto, benefícios anteriores não serão restaurados e você começará como um novo membro."
      }
    ]
  }
];

export default function FAQ() {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-gray-800 to-gray-900 text-white py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6">
              Perguntas Frequentes
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl mb-8 max-w-3xl mx-auto px-4">
              Encontre respostas para as principais dúvidas sobre KKVIP. 
              Ainda tem perguntas? Nossa equipe está aqui para ajudar 24/7!
            </p>
          </div>
        </div>
      </section>

      {/* Search Section */}
      <section className="py-12 bg-gray-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-white mb-6">
              Procure sua dúvida
            </h2>
            <div className="relative max-w-xl mx-auto">
              <input
                type="text"
                placeholder="Digite sua pergunta aqui..."
                className="w-full px-4 py-3 pl-12 pr-4 text-white bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500 focus:bg-gray-600"
              />
              <div className="absolute inset-y-0 left-0 flex items-center pl-4">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Categories */}
      <section className="py-16">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {faqs.map((category, categoryIndex) => (
            <div key={categoryIndex} className="mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                {category.category}
              </h2>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {category.questions.map((faq, faqIndex) => (
                  <div key={faqIndex} className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">
                      {faq.q}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {faq.a}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Contact Support */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Não encontrou sua resposta?
          </h2>
          <p className="text-xl mb-8">
            Nossa equipe de suporte está disponível 24/7 para ajudar você com qualquer dúvida
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Chat ao Vivo</h3>
              <p className="text-blue-100">Resposta em até 5 minutos</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">E-mail</h3>
              <p className="text-blue-100"><EMAIL></p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Telefone</h3>
              <p className="text-blue-100">0800-123-KKVIP</p>
            </div>
          </div>
          
          <div className="bg-white bg-opacity-10 rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-3">Dica para um atendimento mais rápido:</h3>
            <p className="text-blue-100">
              Tenha em mãos seu e-mail de cadastro e descreva sua dúvida com o máximo de detalhes possível. 
              Isso nos ajuda a resolver sua questão mais rapidamente!
            </p>
          </div>
        </div>
      </section>

      {/* Quick Start CTA */}
      <section className="py-16 bg-gray-100">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Pronto para começar?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Agora que você tirou suas dúvidas, que tal experimentar o KKVIP gratuitamente?
          </p>
          <a 
            href="https://1wuafz.life/?open=register&p=7fjh"
            target="_blank"
            rel="noopener noreferrer"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-base sm:text-lg font-semibold transition-colors inline-block"
          >
            Cadastre-se Grátis Agora
          </a>
          <p className="text-sm text-gray-500 mt-4">
            ✅ 100% Gratuito • ✅ Ativação Instantânea • ✅ Suporte 24/7
          </p>
        </div>
      </section>
    </div>
  );
}