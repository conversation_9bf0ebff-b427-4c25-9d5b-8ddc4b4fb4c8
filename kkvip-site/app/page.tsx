import Link from 'next/link';
import HeroBanner from '@/components/HeroBanner';
import { Metadata } from 'next';
import { AffiliateOffers } from '@/components/AffiliateLinks';
import { RelatedPages } from '@/components/InternalLinks';

export const metadata: Metadata = {
  title: 'KKVIP Brasil - Plataforma Online Líder em Benefícios',
  description: 'Descubra KKVIP, a plataforma online líder no Brasil. Cadastre-se grátis e aproveite promoções exclusivas, bônus especiais e benefícios únicos para maximizar sua experiência online.',
  keywords: ['kkvip', 'kkvip brasil', 'kkvip online', 'kkvip cadastro', 'kkvip grátis', 'plataforma online brasil', 'benefícios online', 'promoções exclusivas'],
  openGraph: {
    title: 'KKVIP Brasil - Plataforma Online Líder em Benefícios',
    description: 'Cadastre-se gr<PERSON><PERSON> no KKVIP e aproveite benefícios exclusivos.',
  },
};

export default function Home() {
  return (
    <>
      {/* Hero Banner */}
      <HeroBanner />

      {/* Features Section */}
      <section className="py-12 sm:py-16 md:py-20 bg-gray-800">
        <div className="container mx-auto px-6 sm:px-8 lg:px-12 max-w-6xl">
          <div className="text-center mb-12 sm:mb-16 space-y-6">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white leading-tight">
              Por que escolher o KKVIP?
            </h2>
            <p className="text-base sm:text-lg md:text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed px-4">
              Descubra os benefícios exclusivos que fazem do KKVIP a escolha número 1 no Brasil
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 px-2">
            <div className="text-center p-6 sm:p-8 bg-gray-700 rounded-xl shadow-lg">
              <div className="w-16 h-16 sm:w-20 sm:h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 sm:w-10 sm:h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <h3 className="text-lg sm:text-xl font-bold mb-4 text-white">Bônus Exclusivos</h3>
              <p className="text-sm sm:text-base text-gray-300 leading-relaxed px-2">
                Receba bônus especiais e promoções exclusivas disponíveis apenas para membros KKVIP.
              </p>
            </div>

            <div className="text-center p-6 sm:p-8 bg-gray-700 rounded-xl shadow-lg">
              <div className="w-16 h-16 sm:w-20 sm:h-20 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 sm:w-10 sm:h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg sm:text-xl font-bold mb-4 text-white">100% Confiável</h3>
              <p className="text-sm sm:text-base text-gray-300 leading-relaxed px-2">
                Plataforma segura e confiável com milhares de usuários satisfeitos em todo o Brasil.
              </p>
            </div>

            <div className="text-center p-6 sm:p-8 bg-gray-700 rounded-xl shadow-lg sm:col-span-2 lg:col-span-1">
              <div className="w-16 h-16 sm:w-20 sm:h-20 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 sm:w-10 sm:h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-lg sm:text-xl font-bold mb-4 text-white">Acesso Rápido</h3>
              <p className="text-sm sm:text-base text-gray-300 leading-relaxed px-2">
                Cadastro gratuito em menos de 2 minutos. Comece a aproveitar os benefícios imediatamente.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 sm:py-16 md:py-20 bg-gray-900 text-white">
        <div className="container mx-auto px-6 sm:px-8 lg:px-12 max-w-4xl text-center">
          <div className="space-y-8 sm:space-y-10">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold leading-tight">
              Pronto para começar sua jornada KKVIP?
            </h2>
            <p className="text-base sm:text-lg md:text-xl leading-relaxed max-w-4xl mx-auto px-4">
              Junte-se a milhares de brasileiros que já descobriram os benefícios exclusivos do KKVIP. Cadastre-se grátis hoje mesmo!
            </p>
            <div className="max-w-sm mx-auto px-6">
              <Link
                href="https://1wuafz.life/?open=register&p=7fjh"
                target="_blank"
                rel="noopener noreferrer"
                className="block bg-yellow-400 hover:bg-yellow-500 text-gray-900 px-8 py-4 rounded-lg text-base sm:text-lg font-bold transition-colors shadow-lg min-h-[56px] flex items-center justify-center"
              >
                Começar Agora - Grátis!
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Preview */}
      <section className="py-12 sm:py-16 md:py-20 bg-gray-800">
        <div className="container mx-auto px-6 sm:px-8 lg:px-12 max-w-5xl">
          <div className="text-center mb-12 sm:mb-16 space-y-6">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white leading-tight">
              Perguntas Frequentes
            </h2>
            <p className="text-base sm:text-lg md:text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed px-4">
              Tudo o que você precisa saber sobre o KKVIP
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-6 px-2">
            <div className="bg-gray-700 p-6 sm:p-8 rounded-xl shadow-lg">
              <h3 className="text-base sm:text-lg font-bold mb-4 text-white">O KKVIP é realmente grátis?</h3>
              <p className="text-sm sm:text-base text-gray-300 leading-relaxed px-2">
                Sim! O cadastro e uso básico do KKVIP são completamente gratuitos. Você pode acessar todos os benefícios principais sem pagar nada.
              </p>
            </div>

            <div className="bg-gray-700 p-6 sm:p-8 rounded-xl shadow-lg">
              <h3 className="text-base sm:text-lg font-bold mb-4 text-white">Como funciona o KKVIP?</h3>
              <p className="text-sm sm:text-base text-gray-300 leading-relaxed px-2">
                Após se cadastrar, você terá acesso a promoções exclusivas, bônus especiais e benefícios únicos disponíveis apenas para membros da plataforma.
              </p>
            </div>

            <div className="text-center mt-8 sm:mt-10">
              <Link
                href="/faq"
                className="text-yellow-400 hover:text-yellow-300 font-semibold text-sm sm:text-base inline-flex items-center gap-2"
              >
                Ver todas as perguntas frequentes
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Affiliate Offers */}
      <AffiliateOffers />

      {/* Related Pages */}
      <RelatedPages currentPage="home" />
    </>
  );
}
