<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="orangeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B35;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FF5722;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E55100;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Background -->
  <rect width="32" height="32" rx="6" ry="6" fill="url(#orangeGradient)"/>

  <!-- KK text -->
  <text x="16" y="15" font-family="Arial, Helvetica, sans-serif" font-size="9" font-weight="900" fill="white" text-anchor="middle" letter-spacing="-0.5px">KK</text>

  <!-- VIP text -->
  <text x="16" y="26" font-family="Arial, Helvetica, sans-serif" font-size="9" font-weight="900" fill="white" text-anchor="middle" letter-spacing="-0.5px">VIP</text>
</svg>
