<svg width="1200" height="400" viewBox="0 0 1200 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Background gradient -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f3460;stop-opacity:1" />
    </linearGradient>
    
    <!-- Slot machine gradient -->
    <linearGradient id="slotGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2c3e50;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#34495e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2c3e50;stop-opacity:1" />
    </linearGradient>
    
    <!-- Gold gradient for 7s -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
    </linearGradient>
    
    <!-- Red gradient for 7s -->
    <linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FF5252;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F44336;stop-opacity:1" />
    </linearGradient>
    
    <!-- Glow effect -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="400" fill="url(#bgGradient)"/>
  
  <!-- Blurred background elements for depth -->
  <circle cx="100" cy="100" r="50" fill="#FF5722" opacity="0.1"/>
  <circle cx="1100" cy="300" r="80" fill="#FFC107" opacity="0.1"/>
  <circle cx="600" cy="50" r="30" fill="#4CAF50" opacity="0.1"/>
  
  <!-- KKVIP Logo in top left -->
  <g transform="translate(50, 30)">
    <rect width="80" height="80" rx="12" ry="12" fill="url(#redGradient)"/>
    <text x="40" y="35" font-family="Arial, sans-serif" font-size="16" font-weight="900" fill="white" text-anchor="middle">KK</text>
    <text x="40" y="55" font-family="Arial, sans-serif" font-size="16" font-weight="900" fill="white" text-anchor="middle">VIP</text>
  </g>
  
  <!-- Slot machine reels -->
  <g transform="translate(300, 80)">
    <!-- Reel 1 -->
    <rect x="0" y="0" width="120" height="240" rx="15" ry="15" fill="url(#slotGradient)" stroke="#555" stroke-width="2"/>
    <rect x="10" y="10" width="100" height="220" rx="10" ry="10" fill="#1a1a1a"/>
    
    <!-- BAR text -->
    <text x="60" y="40" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFD700" text-anchor="middle">BAR</text>
    
    <!-- Number 7 -->
    <text x="60" y="120" font-family="Arial, sans-serif" font-size="60" font-weight="900" fill="url(#redGradient)" text-anchor="middle" filter="url(#glow)">7</text>
    
    <!-- BAR text bottom -->
    <text x="60" y="200" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFD700" text-anchor="middle">BAR</text>
  </g>
  
  <g transform="translate(450, 80)">
    <!-- Reel 2 -->
    <rect x="0" y="0" width="120" height="240" rx="15" ry="15" fill="url(#slotGradient)" stroke="#555" stroke-width="2"/>
    <rect x="10" y="10" width="100" height="220" rx="10" ry="10" fill="#1a1a1a"/>
    
    <!-- BAR text -->
    <text x="60" y="40" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFD700" text-anchor="middle">BAR</text>
    
    <!-- Number 7 -->
    <text x="60" y="120" font-family="Arial, sans-serif" font-size="60" font-weight="900" fill="url(#redGradient)" text-anchor="middle" filter="url(#glow)">7</text>
    
    <!-- BAR text bottom -->
    <text x="60" y="200" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFD700" text-anchor="middle">BAR</text>
  </g>
  
  <g transform="translate(600, 80)">
    <!-- Reel 3 -->
    <rect x="0" y="0" width="120" height="240" rx="15" ry="15" fill="url(#slotGradient)" stroke="#555" stroke-width="2"/>
    <rect x="10" y="10" width="100" height="220" rx="10" ry="10" fill="#1a1a1a"/>
    
    <!-- BAR text -->
    <text x="60" y="40" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFD700" text-anchor="middle">BAR</text>
    
    <!-- Number 7 -->
    <text x="60" y="120" font-family="Arial, sans-serif" font-size="60" font-weight="900" fill="url(#redGradient)" text-anchor="middle" filter="url(#glow)">7</text>
    
    <!-- BAR text bottom -->
    <text x="60" y="200" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFD700" text-anchor="middle">BAR</text>
  </g>
  
  <!-- Winning line indicator -->
  <line x1="250" y1="200" x2="770" y2="200" stroke="#FFD700" stroke-width="3" opacity="0.8"/>
  
  <!-- Sparkle effects -->
  <g opacity="0.6">
    <circle cx="200" cy="150" r="3" fill="#FFD700">
      <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="800" cy="180" r="2" fill="#FFF">
      <animate attributeName="opacity" values="0;1;0" dur="1.5s" repeatCount="indefinite" begin="0.5s"/>
    </circle>
    <circle cx="250" cy="120" r="2" fill="#FFD700">
      <animate attributeName="opacity" values="0;1;0" dur="1.8s" repeatCount="indefinite" begin="1s"/>
    </circle>
  </g>
  
  <!-- Text overlay -->
  <g transform="translate(850, 150)">
    <text x="0" y="0" font-family="Arial, sans-serif" font-size="36" font-weight="900" fill="#FFD700" filter="url(#glow)">JACKPOT!</text>
    <text x="0" y="40" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="#FFF">Ganhe Grandes Prêmios</text>
    <text x="0" y="65" font-family="Arial, sans-serif" font-size="16" font-weight="400" fill="#CCC">com KKVIP Brasil</text>
  </g>
</svg>
