<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="orangeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B35;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FF5722;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E55100;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Background -->
  <rect width="200" height="200" rx="25" ry="25" fill="url(#orangeGradient)"/>

  <!-- KK text -->
  <text x="100" y="85" font-family="Arial, Helvetica, sans-serif" font-size="52" font-weight="900" fill="white" text-anchor="middle" letter-spacing="-2px">KK</text>

  <!-- VIP text -->
  <text x="100" y="145" font-family="Arial, Helvetica, sans-serif" font-size="52" font-weight="900" fill="white" text-anchor="middle" letter-spacing="-1px">VIP</text>
</svg>
